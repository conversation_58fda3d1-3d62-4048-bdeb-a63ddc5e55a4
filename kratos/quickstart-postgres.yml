version: "3.7"

services:
  kratos-migrate:
    environment:
      - DSN=***************************************/kratos?sslmode=disable&max_conns=20&max_idle_conns=4

  kratos:
    environment:
      - DSN=***************************************/kratos?sslmode=disable&max_conns=20&max_idle_conns=4

  postgresd:
    image: postgres:14
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=kratos
      - POSTGRES_PASSWORD=secret
      - POSTGRES_DB=kratos
    networks:
      - intranet
