#!/bin/bash

echo "Stopping Kratos containers..."
docker-compose -f quickstart.yml down

echo "Starting Kratos with new configuration..."
docker-compose -f quickstart.yml up -d

echo "Waiting for Krato<PERSON> to be ready..."
sleep 10

echo "Checking Kratos health..."
curl -s http://localhost:4433/health/ready

echo ""
echo "Kratos is now running with CORS enabled for localhost:3000"
echo "Public API: http://localhost:4433"
echo "Admin API: http://localhost:4434"