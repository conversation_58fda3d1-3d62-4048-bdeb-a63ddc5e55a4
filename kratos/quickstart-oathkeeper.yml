version: '3.7'

services:
  kratos:
    environment:
      - SERVE_PUBLIC_BASE_URL=http://127.0.0.1:4455/.ory/kratos/public/

  kratos-selfservice-ui-node:
    environment:
      - PORT=4435
      - KRATOS_BROWSER_URL=http://127.0.0.1:4455/.ory/kratos/public
      - JWKS_URL=http://oathkeeper:4456/.well-known/jwks.json
      - SECURITY_MODE=jwks

  oathkeeper:
    image: oryd/oathkeeper:v0.40
    depends_on:
      - kratos
    ports:
      - 4455:4455
      - 4456:4456
    command:
      serve proxy -c "/etc/config/oathkeeper/oathkeeper.yml"
    environment:
      - LOG_LEVEL=debug
    restart: on-failure
    networks:
      - intranet
    volumes:
      - ./contrib/quickstart/oathkeeper:/etc/config/oathkeeper
