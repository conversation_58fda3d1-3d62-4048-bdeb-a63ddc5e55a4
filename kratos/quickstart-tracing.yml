###########################################################################
#######             FOR DEMONSTRATION PURPOSES ONLY                 #######
###########################################################################
#                                                                         #
# If you have not yet read the tutorial, do so now:                       #
#  https://www.ory.sh/docs/hydra/5min-tutorial                            #
#                                                                         #
# This set up is only for demonstration purposes. The login               #
# endpoint can only be used if you follow the steps in the tutorial.      #
#                                                                         #
###########################################################################

version: '3.7'

services:

  kratos:
    depends_on:
      - jaeger
    environment:
      - TRACING_PROVIDER=jaeger
      ### Jaeger ###
      - TRACING_PROVIDERS_JAEGER_SAMPLING_SERVER_URL=http://jaeger:5778/sampling
      - TRACING_PROVIDERS_JAEGER_LOCAL_AGENT_ADDRESS=jaeger:6831
  jaeger:
    image: jaegertracing/all-in-one:1.19.2
    ports:
      - "16686:16686" # The UI port
    networks:
      - intranet

