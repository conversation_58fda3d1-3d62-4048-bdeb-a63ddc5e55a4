version: v0.13.0

dsn: memory

serve:
  public:
    base_url: http://localhost:4433/
    cors:
      enabled: true
  admin:
    base_url: http://kratos:4434/

session:
  whoami:
    required_aal: aal1

selfservice:
  default_browser_return_url: http://localhost:4455/
  allowed_return_urls:
    - http://localhost:4455
    - http://localhost:19006/Callback
    - exp://localhost:8081/--/Callback

  methods:
    password:
      enabled: true
    webauthn:
      enabled: true
      config:
        passwordless: true
        rp:
          display_name: Your Application name
          # Set 'id' to the top-level domain.
          id: localhost
          # Set 'origin' to the exact URL of the page that prompts the user to use WebAuthn. You must include the scheme, host, and port.
          origin: http://localhost:4455
    passkey:
      enabled: true
      config:
        rp:
          display_name: Your Application name
          # Set 'id' to the top-level domain.
          id: localhost
          # Set 'origin' to the exact URL of the page that prompts the user to use WebAuthn. You must include the scheme, host, and port.
          origins:
            - http://localhost:4455

  flows:
    error:
      ui_url: http://localhost:4455/error

    settings:
      ui_url: http://localhost:4455/settings
      privileged_session_max_age: 15m
      required_aal: aal1

    recovery:
      enabled: true
      ui_url: http://localhost:4455/recovery
      use: code

    verification:
      enabled: false
      ui_url: http://localhost:4455/verification
      use: code
      after:
        default_browser_return_url: http://localhost:4455/

    logout:
      after:
        default_browser_return_url: http://localhost:4455/login

    login:
      ui_url: http://localhost:4455/login
      lifespan: 10m

    registration:
      enable_legacy_one_step: false
      lifespan: 10m
      ui_url: http://localhost:4455/registration
      after:
        passkey:
          hooks:
            - hook: session
        webauthn:
          hooks:
            - hook: session
        password:
          hooks:
            - hook: session
            - hook: show_verification_ui

log:
  level: debug
  format: text
  leak_sensitive_values: true

secrets:
  cookie:
    - PLEASE-CHANGE-ME-I-AM-VERY-INSECURE
  cipher:
    - 32-LONG-SECRET-NOT-SECURE-AT-ALL

ciphers:
  algorithm: xchacha20-poly1305

hashers:
  algorithm: bcrypt
  bcrypt:
    cost: 8

identity:
  default_schema_id: default
  schemas:
    - id: default
      url: file://./contrib/quickstart/kratos/all-strategies/identity.schema.json

courier:
  smtp:
    connection_uri: smtps://test:test@mailslurper:1025/?skip_ssl_verify=true
