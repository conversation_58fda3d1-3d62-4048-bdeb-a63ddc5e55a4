version: v0.13.0

dsn: sqlite:///var/lib/sqlite/db.sqlite?_fk=true

serve:
  public:
    base_url: http://localhost:4433/
    cors:
      enabled: true
      allowed_origins:
        - http://localhost:3000
        - http://127.0.0.1:3000
      allowed_methods:
        - POST
        - GET
        - PUT
        - PATCH
        - DELETE
      allowed_headers:
        - Authorization
        - Cookie
        - Content-Type
        - X-Session-Token
      exposed_headers:
        - Content-Type
        - Set-Cookie
      allow_credentials: true
      debug: true
  admin:
    base_url: http://localhost:4434/

selfservice:
  default_browser_return_url: http://localhost:3000/
  allowed_return_urls:
    - http://localhost:3000
    - http://localhost:3000/
    - http://localhost:3000/workspace
    - http://localhost:3000/auth/login
    - http://localhost:3000/auth/register
    - http://localhost:3000/auth/forgot-password
    - http://localhost:3000/auth/verify-otp
    - http://localhost:3000/auth/settings
    - http://localhost:3000/auth/reset-password

  methods:
    password:
      enabled: true
    totp:
      config:
        issuer: Ford
      enabled: true
    lookup_secret:
      enabled: true
    link:
      enabled: true
    code:
      enabled: true

  flows:
    error:
      ui_url: http://localhost:3000/auth/error

    settings:
      ui_url: http://localhost:3000/auth/settings
      privileged_session_max_age: 15m
      required_aal: aal1
      after:
        password:
          hooks:
            - hook: revoke_active_sessions
          default_browser_return_url: http://localhost:3000/auth/login?message=password_reset_success

    recovery:
      enabled: true
      ui_url: http://localhost:3000/auth/forgot-password
      use: code
      after:
        default_browser_return_url: http://localhost:3000/auth/settings
        hooks:
          - hook: revoke_active_sessions
      notify_unknown_recipients: false
      lifespan: 10m

    verification:
      enabled: true
      ui_url: http://localhost:3000/auth/verification
      use: code
      after:
        default_browser_return_url: http://localhost:3000/

    logout:
      after:
        default_browser_return_url: http://localhost:3000/auth/login

    login:
      ui_url: http://localhost:3000/auth/login
      lifespan: 10m

    registration:
      lifespan: 10m
      ui_url: http://localhost:3000/auth/register
      after:
        password:
          hooks:
            - hook: session
        default_browser_return_url: http://localhost:3000/

log:
  level: debug
  format: text
  leak_sensitive_values: true

secrets:
  cookie:
    - PLEASE-CHANGE-ME-I-AM-VERY-INSECURE
  cipher:
    - 32-LONG-SECRET-NOT-SECURE-AT-ALL

ciphers:
  algorithm: xchacha20-poly1305

hashers:
  algorithm: bcrypt
  bcrypt:
    cost: 8

identity:
  default_schema_id: default
  schemas:
    - id: default
      url: file:///etc/config/kratos/identity.schema.json

courier:
  smtp:
    connection_uri: smtps://test:test@mailslurper:1025/?skip_ssl_verify=true

session:
  lifespan: 24h
  cookie:
    same_site: Lax
