#!/bin/bash

# Development services management script for Ford API

set -e

COMPOSE_FILE="docker-compose.yml"

case "$1" in
    up)
        echo "🚀 Starting development services (PostgreSQL + MinIO)..."
        docker-compose -f $COMPOSE_FILE up -d
        echo ""
        echo "✅ Services started!"
        echo "📊 PostgreSQL: localhost:5432"
        echo "📦 MinIO API: localhost:9000"
        echo "🌐 MinIO Console: http://localhost:9001 (minioadmin:minioadmin)"
        echo ""
        echo "Run 'docker-compose logs -f' to see logs"
        ;;
    down)
        echo "🛑 Stopping development services..."
        docker-compose -f $COMPOSE_FILE down
        echo "✅ Services stopped!"
        ;;
    restart)
        echo "🔄 Restarting development services..."
        docker-compose -f $COMPOSE_FILE down
        docker-compose -f $COMPOSE_FILE up -d
        echo "✅ Services restarted!"
        ;;
    logs)
        docker-compose -f $COMPOSE_FILE logs -f
        ;;
    status)
        docker-compose -f $COMPOSE_FILE ps
        ;;
    clean)
        echo "🧹 Cleaning up development services and volumes..."
        docker-compose -f $COMPOSE_FILE down -v
        docker-compose -f $COMPOSE_FILE down --rmi local
        echo "✅ Cleanup complete!"
        ;;
    *)
        echo "Usage: $0 {up|down|restart|logs|status|clean}"
        echo ""
        echo "Commands:"
        echo "  up      - Start PostgreSQL and MinIO services"
        echo "  down    - Stop all services"
        echo "  restart - Restart all services"
        echo "  logs    - Show logs from all services"
        echo "  status  - Show status of all services"
        echo "  clean   - Stop services and remove volumes/images"
        exit 1
        ;;
esac 