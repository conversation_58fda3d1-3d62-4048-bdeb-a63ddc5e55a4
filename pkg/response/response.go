package response

import (
	"math"
	"net/http"

	"github.com/gin-gonic/gin"
)

type Response struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
}

type PaginatedResponse struct {
	Success    bool        `json:"success"`
	Message    string      `json:"message"`
	Data       interface{} `json:"data,omitempty"`
	Error      string      `json:"error,omitempty"`
	Pagination Pagination  `json:"pagination"`
}

type Pagination struct {
	Page       int   `json:"page"`
	PageSize   int   `json:"page_size"`
	Total      int64 `json:"total"`
	TotalPages int   `json:"total_pages"`
}

func Success(c *gin.Context, statusCode int, message string, data interface{}) {
	c.JSON(statusCode, Response{
		Success: true,
		Message: message,
		Data:    data,
	})
}

func Error(c *gin.Context, statusCode int, message string, error string) {
	c.<PERSON>(statusCode, Response{
		Success: false,
		Message: message,
		Error:   error,
	})
}

func Paginated(c *gin.Context, statusCode int, message string, data interface{}, page, pageSize int, total int64) {
	totalPages := int(math.Ceil(float64(total) / float64(pageSize)))

	c.JSON(statusCode, PaginatedResponse{
		Success: true,
		Message: message,
		Data:    data,
		Pagination: Pagination{
			Page:       page,
			PageSize:   pageSize,
			Total:      total,
			TotalPages: totalPages,
		},
	})
}

func InternalServerError(c *gin.Context) {
	Error(c, http.StatusInternalServerError, "Internal server error", "Something went wrong")
}

func NotFound(c *gin.Context) {
	Error(c, http.StatusNotFound, "Resource not found", "The requested resource was not found")
}

func BadRequest(c *gin.Context, message string) {
	Error(c, http.StatusBadRequest, "Bad request", message)
}

func Unauthorized(c *gin.Context) {
	Error(c, http.StatusUnauthorized, "Unauthorized", "Authentication required")
}

func Forbidden(c *gin.Context) {
	Error(c, http.StatusForbidden, "Forbidden", "Access denied")
} 