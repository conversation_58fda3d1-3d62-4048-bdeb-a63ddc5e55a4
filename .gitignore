# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Build output
/bin/
/build/
/dist/

# Environment files
.env
.env.local
.env.development
.env.test
.env.production

# Logs
*.log
logs/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Temporary files
*.tmp
*.temp
/tmp/

# Database files
*.db
*.sqlite
*.sqlite3

# Docker
.dockerignore

# Air (live reload tool)
tmp/
.air.toml

# Coverage files
coverage.out
coverage.html
*.cover

# Profiling files
*.prof
*.pprof

# Test cache
.cache/

# Local development files
/scripts/*.local
/configs/*.local

# MinIO data (if running locally)
/minio-data/

# PostgreSQL data (if running locally)
/postgres-data/

# Backup files
*.bak
*.backup

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db 