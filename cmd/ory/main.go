package main

import (
	"errors"
	"fmt"
	"log"
	"net/http"
	"os"

	"github.com/gin-gonic/gin"
	ory "github.com/ory/client-go"
	"go.uber.org/zap"

	"ford-api/internal/config"
	"ford-api/internal/endpoint"
	"ford-api/internal/middleware"
)

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatal("Failed to load config:", err)
	}

	// Initialize logger
	logger, err := zap.NewProduction()
	if err != nil {
		log.Fatal("Failed to initialize logger:", err)
	}
	defer logger.Sync()

	// Initialize Ory Kratos
	oryConf := ory.NewConfiguration()
	oryConf.Servers = ory.ServerConfigurations{{URL: cfg.OryKratos.TunnelUrl}}
	oryClient := ory.NewAPIClient(oryConf)

	router := gin.New()
	oryAuthSession := middleware.OrySessionGinMiddleware(logger, oryClient, cfg)
	router.GET("/authn", oryAuthSession, endpoint.TestAuthHandler)

	port := os.Getenv("PORT")
	if port == "" {
		port = "3000"
	}

	fmt.Printf("Application launched and running on http://127.0.0.1:%s\n", port)
	// fmt.Printf("Make sure to run Ory Tunnel in another terminal:\n")
	// fmt.Printf("npx @ory/cli tunnel --dev http://localhost:%s\n", port)

	server := &http.Server{
		Addr:    ":" + port,
		Handler: router,
	}

	// start the server
	err = server.ListenAndServe()
	if errors.Is(err, http.ErrServerClosed) {
		fmt.Println("Server closed")
		return
	}
	fmt.Printf("Could not start server: %s\n", err)
}
