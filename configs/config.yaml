server:
  port: "8080"
  mode: "debug" # debug, release, test
  timeout: 30s

database:
  host: "localhost"
  port: "5432"
  user: "postgres"
  password: "password"
  dbname: "ford_api"
  sslmode: "disable"
  max_open_conns: 25
  max_idle_conns: 25
  conn_max_lifetime: 5m

redis:
  host: "localhost"
  port: "6379"
  password: ""
  db: 0

logging:
  level: "info"
  format: "json"

jwt:
  secret: "your-secret-key"
  expires_in: "24h" 