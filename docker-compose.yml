services:
  postgres:
    image: postgres:17
    container_name: ford-api-postgres
    environment:
      POSTGRES_DB: ford
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - intranet

  minio:
    image: minio/minio:latest
    container_name: ford-api-minio
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin
    ports:
      - "9000:9000"  # MinIO API
      - "9001:9001"  # MinIO Console
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  kratos-migrate:
    image: oryd/kratos:v1.2.0
    container_name: ford-kratos-migrate
    environment:
      - DSN=******************************************/ford?sslmode=disable&max_conns=20&max_idle_conns=4
    volumes:
      - type: bind
        source: ./kratos/contrib/quickstart/kratos/email-password
        target: /etc/config/kratos
    command: -c /etc/config/kratos/kratos.yml migrate sql -e --yes
    restart: on-failure
    depends_on:
      - postgres
    networks:
      - intranet

  kratos:
    image: oryd/kratos:v1.2.0
    container_name: ford-kratos
    depends_on:
      - kratos-migrate
    ports:
      - '4433:4433' # public
      - '4434:4434' # admin
    restart: unless-stopped
    environment:
      - DSN=******************************************/ford?sslmode=disable&max_conns=20&max_idle_conns=4
      - LOG_LEVEL=trace
    command: serve -c /etc/config/kratos/kratos.yml --dev --watch-courier
    volumes:
      - type: bind
        source: ./kratos/contrib/quickstart/kratos/email-password
        target: /etc/config/kratos
    networks:
      - intranet

  mailslurper:
    image: oryd/mailslurper:latest-smtps
    ports:
      - '4436:4436'
      - '4437:4437'
    networks:
      - intranet

networks:
  intranet:

volumes:
  postgres_data:
    driver: local

  minio_data:
    driver: local
