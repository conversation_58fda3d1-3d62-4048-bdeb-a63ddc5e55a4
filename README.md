# Ford API

A Golang REST API built with Gin-Gonic, featuring clean architecture and file upload capabilities.

## 🏗️ Project Structure

```
ford-api/
├── cmd/
│   └── server/
│       └── main.go                 # Application entry point
├── configs/
│   └── config.yaml                 # Configuration file
├── internal/
│   ├── config/
│   │   └── config.go              # Configuration management
│   ├── di/
│   │   ├── app.go                 # Dependency injection setup
│   │   └── router.go              # Router configuration
│   ├── endpoint/
│   │   ├── user_handler.go        # User HTTP handlers
│   │   └── file_handler.go        # File HTTP handlers
│   ├── middleware/
│   │   └── auth.go                # Authentication middleware
│   ├── models/
│   │   └── user.go                # Data models
│   ├── repository/
│   │   ├── interfaces.go          # Repository interfaces
│   │   └── user_repository.go     # Data access layer
│   └── service/
│       ├── interfaces.go          # Service interfaces
│       ├── user_service.go        # Business logic layer
│       ├── auth_service.go        # Authentication service
│       └── file_service.go        # File upload service
├── pkg/
│   ├── database/
│   │   └── postgres.go            # Database connection
│   └── response/
│       └── response.go            # Standardized API responses
├── go.mod                         # Go modules
├── env.example                    # Environment variables example
└── README.md                      # Project documentation
```

## 🔧 Installation & Local Setup

### Prerequisites
- Go 1.21 or higher
- Docker and Docker Compose (for services)

### 1. Clone and Install Dependencies

```bash
git clone <repository-url>
cd ford-api
go mod download
```

### 2. Set up Environment Variables

```bash
cp env.example .env
# Edit .env with your configuration if needed
```

### 3. Start Services with Docker Compose

```bash
# Start PostgreSQL and MinIO services
docker-compose up -d

# Or use the helper script
./scripts/dev-services.sh up
```

This will start:
- **PostgreSQL** on `localhost:5432`
- **MinIO API** on `localhost:9000` 
- **MinIO Console** on `http://localhost:9001` (login: `minioadmin:minioadmin`)

### 4. Run the Application

```bash
go run cmd/server/main.go
```

The API will be available at `http://localhost:8080`

### 5. Test the API

```bash
# Health check
curl http://localhost:8080/health

# Get file upload URL
curl -X POST http://localhost:8080/api/v1/workspaces/test123/files/upload-request \
  -H "Content-Type: application/json" \
  -d '{"filename": "test.pdf", "content_type": "application/pdf"}'
```

### 6. Manage Services

```bash
# Stop services
docker-compose down
# or
./scripts/dev-services.sh down

# View logs
docker-compose logs -f
# or
./scripts/dev-services.sh logs

# Check service status
./scripts/dev-services.sh status

# Clean up everything (including volumes)
./scripts/dev-services.sh clean
```

### Ory Kratos
- Docs: https://www.ory.sh/docs/kratos/quickstart
