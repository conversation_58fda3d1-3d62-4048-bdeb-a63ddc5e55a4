.PHONY: build run test clean tidy fmt vet

# Go parameters
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod
GOFMT=gofmt
GOVET=$(GOCMD) vet

# Binary name
BINARY_NAME=ford-api
BINARY_PATH=./cmd/server

# Build the application
build:
	$(GOBUILD) -o $(BINARY_NAME) -v $(BINARY_PATH)

# Run the application
run:
	$(GOCMD) run $(BINARY_PATH)/main.go

# Run tests
test:
	$(GOTEST) -v ./...

# Run tests with coverage
test-coverage:
	$(GOTEST) -v -coverprofile=coverage.out ./...
	$(GOCMD) tool cover -html=coverage.out

# Clean build artifacts
clean:
	$(GOCLEAN)
	rm -f $(BINARY_NAME)
	rm -f coverage.out



# Tidy up go modules
tidy:
	$(GOMOD) tidy

# Format Go code
fmt:
	$(GOFMT) -s -w .

# Run go vet
vet:
	$(GOVET) ./...

# Run all checks (format, vet, test)
check: fmt vet test

# Install dependencies
deps:
	$(GOGET) -u ./...
	$(GOMOD) tidy

# Run the application in development mode
dev:
	air

# Docker commands
docker-build:
	docker build -t $(BINARY_NAME) .

docker-run:
	docker run -p 8080:8080 $(BINARY_NAME)

# Database commands
db-migrate-up:
	migrate -path migrations -database "postgres://postgres:password@localhost:5432/ford_api?sslmode=disable" up

db-migrate-down:
	migrate -path migrations -database "postgres://postgres:password@localhost:5432/ford_api?sslmode=disable" down

db-migrate-create:
	migrate create -ext sql -dir migrations -seq $(name)

# Help
help:
	@echo "Available commands:"
	@echo "  build         Build the application"
	@echo "  run           Run the application"
	@echo "  test          Run tests"
	@echo "  test-coverage Run tests with coverage"
	@echo "  clean         Clean build artifacts"

	@echo "  tidy          Tidy up go modules"
	@echo "  fmt           Format Go code"
	@echo "  vet           Run go vet"
	@echo "  check         Run fmt, vet, and test"
	@echo "  deps          Install/update dependencies"
	@echo "  dev           Run in development mode with air"
	@echo "  docker-build  Build Docker image"
	@echo "  docker-run    Run Docker container"
	@echo "  help          Show this help message" 