package service

import (
	"context"
	"fmt"
	"ford-api/internal/config"
	"net/url"
	"path/filepath"
	"strings"
	"time"

	"ford-api/internal/models"

	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
	"go.uber.org/zap"
)

type fileService struct {
	minioClient *minio.Client
	bucketName  string
	logger      *zap.Logger
}

func NewFileService(cfg *config.Config, logger *zap.Logger) (FileService, error) {
	// Initialize MinIO client
	minioClient, err := minio.New(cfg.MinIO.Endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(cfg.MinIO.AccessKeyID, cfg.MinIO.SecretAccessKey, ""),
		Secure: cfg.MinIO.UseSSL,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create MinIO client: %w", err)
	}

	service := &fileService{
		minioClient: minioClient,
		bucketName:  cfg.MinIO.BucketName,
		logger:      logger,
	}

	// Ensure bucket exists
	if err := service.ensureBucketExists(context.Background()); err != nil {
		return nil, fmt.Errorf("failed to ensure bucket exists: %w", err)
	}

	return service, nil
}

func (s *fileService) GetPresignedPutURL(ctx context.Context, workspaceID string, filename string, contentType string, expiry time.Duration) (*url.URL, error) {
	objectName := s.getObjectPath(workspaceID, filename)

	// Set request parameters for PUT operation
	reqParams := make(url.Values)
	if contentType != "" {
		reqParams.Set("Content-Type", contentType)
	}

	// Generate presigned PUT URL
	presignedURL, err := s.minioClient.PresignedPutObject(ctx, s.bucketName, objectName, expiry)
	if err != nil {
		s.logger.Error("Failed to generate presigned PUT URL",
			zap.Error(err),
			zap.String("workspace_id", workspaceID),
			zap.String("filename", filename))
		return nil, fmt.Errorf("failed to generate presigned PUT URL: %w", err)
	}

	s.logger.Info("Generated presigned PUT URL",
		zap.String("workspace_id", workspaceID),
		zap.String("filename", filename),
		zap.String("object_name", objectName))

	return presignedURL, nil
}

func (s *fileService) GetPresignedGetURL(ctx context.Context, workspaceID string, filename string, expiry time.Duration) (*url.URL, error) {
	objectName := s.getObjectPath(workspaceID, filename)

	// Generate presigned GET URL
	presignedURL, err := s.minioClient.PresignedGetObject(ctx, s.bucketName, objectName, expiry, nil)
	if err != nil {
		s.logger.Error("Failed to generate presigned GET URL",
			zap.Error(err),
			zap.String("workspace_id", workspaceID),
			zap.String("filename", filename))
		return nil, fmt.Errorf("failed to generate presigned GET URL: %w", err)
	}

	s.logger.Info("Generated presigned GET URL",
		zap.String("workspace_id", workspaceID),
		zap.String("filename", filename),
		zap.String("object_name", objectName))

	return presignedURL, nil
}

func (s *fileService) GetObjectPath(workspaceID string, filename string) string {
	return s.getObjectPath(workspaceID, filename)
}

func (s *fileService) ListFiles(ctx context.Context, workspaceID string) ([]models.FileInfo, error) {
	// Create the prefix for the workspace folder
	prefix := filepath.Join("workspaces", workspaceID) + "/"

	// List objects with the workspace prefix
	objectCh := s.minioClient.ListObjects(ctx, s.bucketName, minio.ListObjectsOptions{
		Prefix:    prefix,
		Recursive: true,
	})

	var files []models.FileInfo
	// Set expiry for presigned URLs to 1 hour
	urlExpiry := 1 * time.Hour

	for object := range objectCh {
		if object.Err != nil {
			s.logger.Error("Error listing object",
				zap.Error(object.Err),
				zap.String("workspace_id", workspaceID))
			return nil, fmt.Errorf("failed to list objects: %w", object.Err)
		}

		// Skip if it's a directory (ends with /)
		if strings.HasSuffix(object.Key, "/") {
			continue
		}

		// Extract filename from the object key
		filename := filepath.Base(object.Key)

		// Generate presigned GET URL for the file
		presignedURL, err := s.minioClient.PresignedGetObject(ctx, s.bucketName, object.Key, urlExpiry, nil)
		if err != nil {
			s.logger.Error("Failed to generate presigned URL for file",
				zap.Error(err),
				zap.String("workspace_id", workspaceID),
				zap.String("filename", filename),
				zap.String("object_key", object.Key))
			return nil, fmt.Errorf("failed to generate presigned URL for file %s: %w", filename, err)
		}

		fileInfo := models.FileInfo{
			Filename:     filename,
			ObjectPath:   object.Key,
			Size:         object.Size,
			LastModified: object.LastModified,
			ContentType:  object.ContentType,
			PreviewURL:   presignedURL.String(),
		}

		files = append(files, fileInfo)
	}

	s.logger.Info("Listed files for workspace",
		zap.String("workspace_id", workspaceID),
		zap.Int("file_count", len(files)))

	return files, nil
}

func (s *fileService) ensureBucketExists(ctx context.Context) error {
	exists, err := s.minioClient.BucketExists(ctx, s.bucketName)
	if err != nil {
		return fmt.Errorf("failed to check if bucket exists: %w", err)
	}

	if !exists {
		err = s.minioClient.MakeBucket(ctx, s.bucketName, minio.MakeBucketOptions{})
		if err != nil {
			return fmt.Errorf("failed to create bucket: %w", err)
		}
		s.logger.Info("Created MinIO bucket", zap.String("bucket", s.bucketName))
	}

	return nil
}

func (s *fileService) getObjectPath(workspaceID string, filename string) string {
	// Create object path: workspaces/{workspace_id}/{filename}
	return filepath.Join("workspaces", workspaceID, filename)
}
