package service

import (
	"context"
	"errors"
	"ford-api/internal/models"
	"ford-api/internal/repository"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type userService struct {
	userRepo    repository.UserRepository
	authService AuthService
	logger      *zap.Logger
}

func NewUserService(userRepo repository.UserRepository, authService AuthService, logger *zap.Logger) UserService {
	return &userService{
		userRepo:    userRepo,
		authService: authService,
		logger:      logger,
	}
}

func (s *userService) CreateUser(ctx context.Context, req *models.CreateUserRequest) (*models.User, error) {
	// Check if user already exists
	existingUser, err := s.userRepo.GetByEmail(ctx, req.Email)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		s.logger.Error("Failed to check existing user", zap.Error(err))
		return nil, err
	}
	if existingUser != nil {
		return nil, errors.New("user with this email already exists")
	}

	// Hash password
	hashedPassword, err := s.authService.HashPassword(req.Password)
	if err != nil {
		s.logger.Error("Failed to hash password", zap.Error(err))
		return nil, err
	}

	// Create user
	user := &models.User{
		Email:    req.Email,
		Name:     req.Name,
		Password: hashedPassword,
		Role:     req.Role,
		IsActive: true,
	}

	if user.Role == "" {
		user.Role = "user"
	}

	if err := s.userRepo.Create(ctx, user); err != nil {
		s.logger.Error("Failed to create user", zap.Error(err))
		return nil, err
	}

	return user, nil
}

func (s *userService) GetUser(ctx context.Context, id uint) (*models.User, error) {
	user, err := s.userRepo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("user not found")
		}
		s.logger.Error("Failed to get user", zap.Error(err))
		return nil, err
	}
	return user, nil
}

func (s *userService) UpdateUser(ctx context.Context, id uint, req *models.UpdateUserRequest) (*models.User, error) {
	// Check if user exists
	user, err := s.userRepo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("user not found")
		}
		s.logger.Error("Failed to get user", zap.Error(err))
		return nil, err
	}

	// Prepare updates
	updates := make(map[string]interface{})
	if req.Name != nil {
		updates["name"] = *req.Name
	}
	if req.Role != nil {
		updates["role"] = *req.Role
	}
	if req.IsActive != nil {
		updates["is_active"] = *req.IsActive
	}

	if len(updates) == 0 {
		return user, nil
	}

	// Update user
	if err := s.userRepo.Update(ctx, id, updates); err != nil {
		s.logger.Error("Failed to update user", zap.Error(err))
		return nil, err
	}

	// Return updated user
	return s.userRepo.GetByID(ctx, id)
}

func (s *userService) DeleteUser(ctx context.Context, id uint) error {
	// Check if user exists
	_, err := s.userRepo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("user not found")
		}
		s.logger.Error("Failed to get user", zap.Error(err))
		return err
	}

	if err := s.userRepo.Delete(ctx, id); err != nil {
		s.logger.Error("Failed to delete user", zap.Error(err))
		return err
	}

	return nil
}

func (s *userService) ListUsers(ctx context.Context, page, pageSize int) ([]*models.User, int64, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	offset := (page - 1) * pageSize

	users, err := s.userRepo.List(ctx, pageSize, offset)
	if err != nil {
		s.logger.Error("Failed to list users", zap.Error(err))
		return nil, 0, err
	}

	total, err := s.userRepo.Count(ctx)
	if err != nil {
		s.logger.Error("Failed to count users", zap.Error(err))
		return nil, 0, err
	}

	return users, total, nil
}

func (s *userService) Login(ctx context.Context, req *models.LoginRequest) (*models.LoginResponse, error) {
	// Get user by email
	user, err := s.userRepo.GetByEmail(ctx, req.Email)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("invalid email or password")
		}
		s.logger.Error("Failed to get user by email", zap.Error(err))
		return nil, err
	}

	// Check if user is active
	if !user.IsActive {
		return nil, errors.New("user account is deactivated")
	}

	// Compare password
	if err := s.authService.ComparePassword(user.Password, req.Password); err != nil {
		return nil, errors.New("invalid email or password")
	}

	// Generate token
	token, err := s.authService.GenerateToken(user.ID, user.Email)
	if err != nil {
		s.logger.Error("Failed to generate token", zap.Error(err))
		return nil, err
	}

	return &models.LoginResponse{
		Token: token,
		User:  *user,
	}, nil
} 