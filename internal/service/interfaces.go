package service

import (
	"context"
	"ford-api/internal/models"
	"net/url"
	"time"
)

type UserService interface {
	CreateUser(ctx context.Context, req *models.CreateUserRequest) (*models.User, error)
	GetUser(ctx context.Context, id uint) (*models.User, error)
	UpdateUser(ctx context.Context, id uint, req *models.UpdateUserRequest) (*models.User, error)
	DeleteUser(ctx context.Context, id uint) error
	ListUsers(ctx context.Context, page, pageSize int) ([]*models.User, int64, error)
	Login(ctx context.Context, req *models.LoginRequest) (*models.LoginResponse, error)
}

type AuthService interface {
	GenerateToken(userID uint, email string) (string, error)
	ValidateToken(token string) (uint, error)
	HashPassword(password string) (string, error)
	ComparePassword(hashedPassword, password string) error
}

type FileService interface {
	GetPresignedPutURL(ctx context.Context, workspaceID string, filename string, contentType string, expiry time.Duration) (*url.URL, error)
	GetPresignedGetURL(ctx context.Context, workspaceID string, filename string, expiry time.Duration) (*url.URL, error)
	GetObjectPath(workspaceID string, filename string) string
	ListFiles(ctx context.Context, workspaceID string) ([]models.FileInfo, error)
}
