package endpoint

import (
	"ford-api/internal/service"
	"ford-api/pkg/response"
	"net/http"
	"time"

	"ford-api/internal/models"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type FileHandler struct {
	fileService service.FileService
	logger      *zap.Logger
}

type UploadRequestRequest struct {
	Filename    string `json:"filename" binding:"required"`
	ContentType string `json:"content_type"`
}

type UploadRequestResponse struct {
	UploadURL  string `json:"upload_url"`
	Filename   string `json:"filename"`
	ObjectPath string `json:"object_path"`
	ExpiresAt  int64  `json:"expires_at"`
}

type ListArtifactsResponse struct {
	Files []models.FileInfo `json:"files"`
	Count int               `json:"count"`
}

func NewFileHandler(fileService service.FileService, logger *zap.Logger) *FileHandler {
	return &FileHandler{
		fileService: fileService,
		logger:      logger,
	}
}

// GetUploadRequest godoc
// @Summary Get presigned upload URL
// @Description Get a presigned URL for uploading a file to a specific workspace
// @Tags files
// @Accept json
// @Produce json
// @Param workspace_id path string true "Workspace ID"
// @Param request body UploadRequestRequest true "Upload request"
// @Success 200 {object} response.Response{data=UploadRequestResponse}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /workspaces/{workspace_id}/files/upload-request [post]
func (h *FileHandler) GetUploadRequest(c *gin.Context) {
	workspaceID := c.Param("workspace_id")
	if workspaceID == "" {
		response.BadRequest(c, "workspace_id is required")
		return
	}

	var req UploadRequestRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Invalid request body", zap.Error(err))
		response.BadRequest(c, err.Error())
		return
	}

	// Set default expiry to 15 minutes
	expiry := 15 * time.Minute
	expiresAt := time.Now().Add(expiry).Unix()

	// Generate presigned PUT URL
	presignedURL, err := h.fileService.GetPresignedPutURL(
		c.Request.Context(),
		workspaceID,
		req.Filename,
		req.ContentType,
		expiry,
	)
	if err != nil {
		h.logger.Error("Failed to generate presigned URL",
			zap.Error(err),
			zap.String("workspace_id", workspaceID),
			zap.String("filename", req.Filename))
		response.InternalServerError(c)
		return
	}

	// Get the object path for the file
	objectPath := h.fileService.GetObjectPath(workspaceID, req.Filename)

	responseData := UploadRequestResponse{
		UploadURL:  presignedURL.String(),
		Filename:   req.Filename,
		ObjectPath: objectPath,
		ExpiresAt:  expiresAt,
	}

	response.Success(c, http.StatusOK, "Upload URL generated successfully", responseData)
}

// ListArtifacts godoc
// @Summary List all artifacts in a workspace
// @Description List all files/artifacts stored in a specific workspace
// @Tags files
// @Accept json
// @Produce json
// @Param workspace_id path string true "Workspace ID"
// @Success 200 {object} response.Response{data=ListArtifactsResponse}
// @Failure 400 {object} response.Response
// @Failure 500 {object} response.Response
// @Router /workspaces/{workspace_id}/artifacts [get]
func (h *FileHandler) ListArtifacts(c *gin.Context) {
	workspaceID := c.Param("workspace_id")
	if workspaceID == "" {
		response.BadRequest(c, "workspace_id is required")
		return
	}

	files, err := h.fileService.ListFiles(c.Request.Context(), workspaceID)
	if err != nil {
		h.logger.Error("Failed to list files",
			zap.Error(err),
			zap.String("workspace_id", workspaceID))
		response.InternalServerError(c)
		return
	}

	responseData := ListArtifactsResponse{
		Files: files,
		Count: len(files),
	}

	response.Success(c, http.StatusOK, "Files listed successfully", responseData)
}
