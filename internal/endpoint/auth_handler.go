package endpoint

import (
	"bytes"
	"encoding/json"
	"html/template"
	"net/http"

	"github.com/gin-gonic/gin"

	"ford-api/internal/middleware"
)

func TestAuthHandler(c *gin.Context) {
	// Get the session from the context. It was added to the context by the sessionMiddleware.
	session, err := middleware.GetSession(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Encode the session data as pretty JSON.
	buffer := &bytes.Buffer{}
	encoder := json.NewEncoder(buffer)
	encoder.SetIndent("", "  ")
	if err := encoder.Encode(session); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Render the dashboard template with the session data.
	err = dashboardTemplate.ExecuteTemplate(c.Writer, "index.html", buffer.String())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
}

var dashboardTemplate *template.Template

func init() {
	var err error
	dashboardTemplate, err = template.New("index.html").Parse(`
<html lang="en">
  <head>
    <title>Ory Network secured Go web app</title>
  </head>
  <body>
    <h1>Dashboard</h1>
    <hr />
    <h2>Your Session Data:</h2>
    <code>{{ . }}</code>
  </body>
</html>
`)
	if err != nil {
		panic(err)
	}
}
