package di

import (
	"net/http"

	"github.com/gin-gonic/gin"
	ory "github.com/ory/client-go"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"ford-api/internal/config"
	"ford-api/internal/endpoint"
	"ford-api/internal/middleware"
	"ford-api/internal/service"
	"ford-api/pkg/database"
	"ford-api/pkg/response"
)

func NewRouter(
	cfg *config.Config,
	logger *zap.Logger,
	db *gorm.DB,
	oryClient *ory.APIClient,
	userHandler *endpoint.UserHandler,
	fileHandler *endpoint.FileHandler,
	authService service.AuthService,
) (*gin.Engine, func(), error) {
	// Set Gin mode
	gin.SetMode(cfg.Server.Mode)

	// Create router
	router := gin.New()

	// Global middleware
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(corsMiddleware())

	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		response.Success(c, http.StatusOK, "Server is healthy", gin.H{
			"status":  "ok",
			"service": "ford-api",
		})
	})

	// Ory AuthN
	oryAuthSession := middleware.OrySessionGinMiddleware(logger, oryClient, cfg)
	router.GET("/authn", oryAuthSession, endpoint.TestAuthHandler)

	// API v1 routes
	v1 := router.Group("/api/v1")

	// Public routes (no authentication required)
	public := v1.Group("")
	{
		// Auth routes
		auth := public.Group("/auth")
		{
			auth.POST("/login", userHandler.Login)
		}

		// Public user routes (if any)
		users := public.Group("/users")
		{
			users.POST("", userHandler.CreateUser) // User registration
		}

		// File routes (no authentication required for now)
		workspaces := public.Group("/workspaces")
		{
			workspaces.POST("/:workspace_id/files/upload-request", fileHandler.GetUploadRequest)
			workspaces.GET("/:workspace_id/artifacts", fileHandler.ListArtifacts)
		}
	}

	// Protected routes (authentication required)
	protected := v1.Group("")
	protected.Use(middleware.AuthMiddleware(authService, logger))
	{
		// User routes
		users := protected.Group("/users")
		{
			users.GET("", userHandler.ListUsers)
			users.GET("/:id", userHandler.GetUser)
			users.PUT("/:id", userHandler.UpdateUser)
			users.DELETE("/:id", userHandler.DeleteUser)
		}
	}

	// Run database migrations
	if err := database.AutoMigrate(db, logger); err != nil {
		logger.Fatal("Failed to run database migrations", zap.Error(err))
		return nil, nil, err
	}

	// Cleanup function
	cleanup := func() {
		if sqlDB, err := db.DB(); err == nil {
			sqlDB.Close()
		}
	}

	return router, cleanup, nil
}

func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Credentials", "true")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With")
		c.Header("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, DELETE")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}
