package di

import (
	"github.com/gin-gonic/gin"
	ory "github.com/ory/client-go"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"ford-api/internal/config"
	"ford-api/internal/endpoint"
	"ford-api/internal/repository"
	"ford-api/internal/service"
	"ford-api/pkg/database"
)

// Application holds all the application dependencies
type Application struct {
	DB          *gorm.DB
	UserHandler *endpoint.UserHandler
	FileHandler *endpoint.FileHandler
	Router      *gin.Engine
	Ory         *ory.APIClient
}

func InitializeApp(cfg *config.Config, logger *zap.Logger) (*Application, func(), error) {
	// Initialize database connection
	db, err := database.NewPostgresConnection(cfg, logger)
	if err != nil {
		return nil, nil, err
	}

	// Initialize Ory Kratos
	oryConf := ory.NewConfiguration()
	oryConf.Servers = ory.ServerConfigurations{{URL: cfg.OryKratos.TunnelUrl}}
	oryClient := ory.NewAPIClient(oryConf)

	// Initialize repositories
	userRepo := repository.NewUserRepository(db)

	// Initialize services
	authService, err := service.NewAuthService(cfg)
	if err != nil {
		return nil, nil, err
	}
	userService := service.NewUserService(userRepo, authService, logger)

	fileService, err := service.NewFileService(cfg, logger)
	if err != nil {
		return nil, nil, err
	}

	// Initialize handlers
	userHandler := endpoint.NewUserHandler(userService, logger)
	fileHandler := endpoint.NewFileHandler(fileService, logger)

	// Initialize router
	router, cleanup, err := NewRouter(cfg, logger, db, oryClient, userHandler, fileHandler, authService)
	if err != nil {
		return nil, cleanup, err
	}

	app := &Application{
		DB:          db,
		UserHandler: userHandler,
		FileHandler: fileHandler,
		Router:      router,
		Ory:         oryClient,
	}

	return app, cleanup, nil
}
