package middleware

import (
	"context"
	"errors"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	ory "github.com/ory/client-go"
	"go.uber.org/zap"

	"ford-api/internal/config"
	"ford-api/internal/service"
	"ford-api/pkg/response"
)

const REQ_SESSION = "req.session"

// func OrySessionMiddleware(next http.HandlerFunc, logger *zap.Logger, oryClient *ory.APIClient, conf *config.OryKratosConfig) http.HandlerFunc {
// 	return func(writer http.ResponseWriter, request *http.Request) {
// 		logger.Info("handling middleware request\n")
//
// 		// This example passes all request.Cookies to `ToSession` function.
// 		//
// 		// However, it is enough to pass only the value of the `ory_session_projectslug` cookie to the endpoint.
// 		cookies := request.Header.Get("Cookie")
//
// 		// A native app would submit the session token instead of a cookie.
// 		// You can look up session tokens the same way by using the `XSessionToken` setter on the `ToSession` function.
//
// 		// Look up session.
// 		session, _, err := oryClient.FrontendAPI.ToSession(request.Context()).Cookie(cookies).Execute()
// 		// Check if a session exists and if it is active.
// 		// You could add your own logic here to check if the session is valid for the specific endpoint, e.g. using the `session.AuthenticatedAt` field.
// 		// Redirect to login if session doesn't exist or is inactive
// 		if err != nil || (err == nil && !*session.Active) {
// 			logger.Info("No active session, redirecting to login\n")
// 			// Redirect to the tunnel URL, not the local app
// 			http.Redirect(writer, request, conf.TunnelUrl+"/ui/login", http.StatusSeeOther)
// 			return
// 		}
//
// 		// Add the session details to the context for handlers to access.
// 		ctx := WithSession(request.Context(), session)
//
// 		// Continue to the next handler (the dashboard in the simple example).
// 		next.ServeHTTP(writer, request.WithContext(ctx))
// 	}
// }

func WithSession(ctx context.Context, v *ory.Session) context.Context {
	return context.WithValue(ctx, REQ_SESSION, v)
}

func GetSession(ctx context.Context) (*ory.Session, error) {
	session, ok := ctx.Value(REQ_SESSION).(*ory.Session)
	if !ok || session == nil {
		return nil, errors.New("session not found in context")
	}
	return session, nil
}

func AuthMiddleware(authService service.AuthService, logger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			response.Error(c, http.StatusUnauthorized, "Authorization header required", "Missing Authorization header")
			c.Abort()
			return
		}

		// Check if the authorization header starts with "Bearer "
		if !strings.HasPrefix(authHeader, "Bearer ") {
			response.Error(c, http.StatusUnauthorized, "Invalid authorization header format", "Authorization header must start with 'Bearer '")
			c.Abort()
			return
		}

		// Extract the token
		token := strings.TrimPrefix(authHeader, "Bearer ")
		if token == "" {
			response.Error(c, http.StatusUnauthorized, "Token required", "Missing token in Authorization header")
			c.Abort()
			return
		}

		// Validate the token
		userID, err := authService.ValidateToken(token)
		if err != nil {
			logger.Error("Token validation failed", zap.Error(err))
			response.Error(c, http.StatusUnauthorized, "Invalid token", err.Error())
			c.Abort()
			return
		}

		// Set user ID in context for use in handlers
		c.Set("user_id", userID)
		c.Next()
	}
}

func OptionalAuthMiddleware(authService service.AuthService, logger *zap.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.Next()
			return
		}

		if strings.HasPrefix(authHeader, "Bearer ") {
			token := strings.TrimPrefix(authHeader, "Bearer ")
			if token != "" {
				userID, err := authService.ValidateToken(token)
				if err == nil {
					c.Set("user_id", userID)
				}
			}
		}

		c.Next()
	}
}

func OrySessionGinMiddleware(logger *zap.Logger, oryClient *ory.APIClient, conf *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		logger.Info("handling middleware request")

		// This example passes all request.Cookies to `ToSession` function.
		//
		// However, it is enough to pass only the value of the `ory_session_projectslug` cookie to the endpoint.
		cookies := c.GetHeader("Cookie")

		// A native app would submit the session token instead of a cookie.
		// You can look up session tokens the same way by using the `XSessionToken` setter on the `ToSession` function.

		// Look up session.
		session, _, err := oryClient.FrontendAPI.ToSession(c.Request.Context()).Cookie(cookies).Execute()
		// Check if a session exists and if it is active.
		// You could add your own logic here to check if the session is valid for the specific endpoint, e.g. using the `session.AuthenticatedAt` field.
		// Redirect to login if session doesn't exist or is inactive
		if err != nil || (err == nil && !*session.Active) {
			logger.Info("No active session, redirecting to login")
			// Redirect to the tunnel URL, not the local app
			c.Redirect(http.StatusSeeOther, conf.OryKratos.LoginUIUrl)
			c.Abort()
			return
		}

		// Add the session details to the context for handlers to access.
		ctx := WithSession(c.Request.Context(), session)
		c.Request = c.Request.WithContext(ctx)

		// Continue to the next handler.
		c.Next()
	}
}
